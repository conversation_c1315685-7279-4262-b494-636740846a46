<template>
  <a-modal
    :width="1000"
    :visible="visible"
    title="手续费详情"
    :mask-closable="false"
    :body-style="{ paddingBottom: '20px' }"
    @update:visible="updateVisible"
  >
    <a-descriptions :column="2" title="基本信息">
      <a-descriptions-item label="代付流水单号">{{ form.withdrawFlowNo }}</a-descriptions-item>
      <a-descriptions-item label="代付金额">{{ form.withdrawAmount }}</a-descriptions-item>
      <a-descriptions-item label="付款用户钱包类型">
        <a-tag v-if="form.walletType === '300'">营销奖励钱包</a-tag>
        <a-tag v-else-if="form.walletType === '600'">分润钱包</a-tag>
        <a-tag v-else-if="form.walletType === '800'">代付D0钱包</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="银行账户类型">
        <a-tag v-if="form.bankAccountType === 'G'" color="orange">对公</a-tag>
        <a-tag v-else-if="form.bankAccountType === 'S'" color="blue">对私</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="付款用户类型" v-if="hasPurview('0')">
        <a-badge v-if="form.userType === 1" color="pink" text="大区" />
        <a-badge v-else-if="form.userType === 2" color="blue" text="运营中心" />
        <a-badge v-else-if="form.userType === 3" color="cyan" text="代理商" />
        <a-badge v-else-if="form.userType === 5" color="orange" text="子级代理商" />
      </a-descriptions-item>
      <a-descriptions-item label="付款用户编号">{{ form.userNo }}</a-descriptions-item>
    </a-descriptions>

    <a-descriptions :column="2" title="平台分润信息" v-if="hasPurview(['0'])">
      <a-descriptions-item label="通道提现费率">{{ form.chnRate }}</a-descriptions-item>
      <a-descriptions-item label="通道提现单笔费用">{{ form.chnSingleFee }}</a-descriptions-item>
      <a-descriptions-item label="通道提现手续费">{{ form.chnFee }}</a-descriptions-item>
      <a-descriptions-item label="平台利润">{{ form.plfProfit }}</a-descriptions-item>
    </a-descriptions>

    <a-descriptions :column="2" title="大区分润信息" v-if="hasPurview(['0', '1']) && (form.regionFee || form.regionProfit)">
      <a-descriptions-item label="大区编号">{{ form.regionNo }}</a-descriptions-item>
      <a-descriptions-item label="大区中心费率">{{ form.regionRate }}</a-descriptions-item>
      <a-descriptions-item label="大区中心单笔费用">{{ form.regionSingleFee }}</a-descriptions-item>
      <a-descriptions-item label="大区中心手续费">{{ form.regionFee }}</a-descriptions-item>
      <a-descriptions-item label="大区中心利润">{{ form.regionProfit }}</a-descriptions-item>
    </a-descriptions>

    <a-descriptions :column="2" title="运营中心分润信息" v-if="hasPurview(['0', '1', '2']) && (form.branchFee || form.branchProfit)">
      <a-descriptions-item label="运营中心编号">{{ form.branchNo }}</a-descriptions-item>
      <a-descriptions-item label="运营中心费率">{{ form.branchRate }}</a-descriptions-item>
      <a-descriptions-item label="运营中心单笔费用">{{ form.branchSingleFee }}</a-descriptions-item>
      <a-descriptions-item label="运营中心手续费">{{ form.branchFee }}</a-descriptions-item>
      <a-descriptions-item label="运营中心利润">{{ form.branchProfit }}</a-descriptions-item>
    </a-descriptions>

    <a-divider orientation="left" :orientationMargin="0" v-if="form.agentFeeObjList?.length">代理商分润信息</a-divider>
    <template v-for="(item, key) in form.agentFeeObjList || []" :key="key">
      <a-descriptions :column="2" v-if="item.agentProfit || item.agentFee">
        <a-descriptions-item label="代理商编号">{{ item.agentNo }}</a-descriptions-item>
        <a-descriptions-item label="代理商费率">{{ item.agentRate }}</a-descriptions-item>
        <a-descriptions-item label="代理商单笔费用">{{ item.agentSingleFee }}</a-descriptions-item>
        <a-descriptions-item label="代理商手续费">{{ item.agentFee }}</a-descriptions-item>
        <a-descriptions-item label="代理商利润">{{ item.agentProfit }}</a-descriptions-item>
      </a-descriptions>
      <a-divider dashed style="margin-bottom: 15px" v-if="key !== form.agentFeeObjList?.length - 1"
      /></template>
    <template #footer>
      <a-button @click="updateVisible(false)">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
import { reactive, toRefs, watchEffect } from 'vue';
import { hasPurview } from '@/utils/permission';

export default {
  props: {
    visible: Boolean,
    detail: Object
  },
  emits: ['update:visible'],
  setup(props, context) {
    const data = reactive({
      form: {},
      hasPurview
    });

    const watch = watchEffect(() => {
      if (props.detail) {
        data.form = Object.assign({}, props.detail);

        data.form.agentFeeObjList = data.form.agentFeeObjList || [];

        if (!hasPurview(['5'])) {
          const oneLevelAgentKeys = Object.keys(props.detail).filter(key => key.startsWith('agent') && key !== 'agentFeeObjList');
          const oneLevelAgentFeeObj = {};
          oneLevelAgentKeys.forEach(key => {
            oneLevelAgentFeeObj[key] = props.detail[key];
          });
          oneLevelAgentFeeObj.agentNo = props.detail.oneLevelAgentNo;
          if (oneLevelAgentFeeObj.agentFee || oneLevelAgentFeeObj.agentProfit) {
            data.form.agentFeeObjList.push(oneLevelAgentFeeObj);
          }
        }
      }
    });

    const updateVisible = value => {
      context.emit('update:visible', value);
    };

    return {
      ...toRefs(data),
      watch,
      updateVisible
    };
  }
};
</script>
